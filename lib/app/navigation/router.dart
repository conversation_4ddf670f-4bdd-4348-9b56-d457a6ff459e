import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/drag_card_page.dart';
import 'package:text_generation_video/app/repository/modals/digital/public_digital_human.dart';
import 'package:text_generation_video/app/repository/modals/voice/creation_result.dart';
import 'package:text_generation_video/app/view/agent/agent_chat_page.dart';
import 'package:text_generation_video/app/view/audio_img_to_video/cartoon_digimon_page.dart';
import 'package:text_generation_video/app/view/audio_img_to_video/sync_sing_page.dart';
import 'package:text_generation_video/app/view/compute_power/compute_power_detail_page.dart';
import 'package:text_generation_video/app/view/compute_power/compute_power_page.dart';
import 'package:text_generation_video/app/view/creation/creation_page.dart';
import 'package:text_generation_video/app/view/creation/creation_state_page.dart';
import 'package:text_generation_video/app/view/digital/custom_digital_page.dart';
import 'package:text_generation_video/app/view/digital/digital_preview_page.dart';
import 'package:text_generation_video/app/view/digital/make_video_digital_page.dart';
import 'package:text_generation_video/app/view/digital/make_video_digital_process_page.dart';
import 'package:text_generation_video/app/view/digital/make_video_page.dart';
import 'package:text_generation_video/app/view/digital/public_digital_detail_page.dart';
import 'package:text_generation_video/app/view/member/member_page.dart';
import 'package:text_generation_video/app/view/audio_img_to_video/pet_sing_page.dart';
import 'package:text_generation_video/app/view/photo_restoration/photo_matting_page.dart';
import 'package:text_generation_video/app/view/photo_restoration/photo_modification_page.dart';
import 'package:text_generation_video/app/view/photo_restoration/photo_portrait_category_page.dart';
import 'package:text_generation_video/app/view/photo_restoration/photo_portrait_page.dart';
import 'package:text_generation_video/app/view/photo_restoration/quality_restoration_page.dart';
import 'package:text_generation_video/app/view/preview/preview_page.dart';
import 'package:text_generation_video/app/view/record/record_shower_page.dart';
import 'package:text_generation_video/app/view/same_style/same_style_detail_page.dart';
import 'package:text_generation_video/app/view/setting/about_us_page.dart';
import 'package:text_generation_video/app/view/setting/collect_message_page.dart';
import 'package:text_generation_video/app/view/setting/contact_us_page.dart';
import 'package:text_generation_video/app/view/setting/not_found_page.dart';
import 'package:text_generation_video/app/view/setting/privacy_center_page.dart';
import 'package:text_generation_video/app/view/setting/privacy_policy_page.dart';
import 'package:text_generation_video/app/view/setting/setting_page.dart';
import 'package:text_generation_video/app/view/setting/user_agreement_page.dart';
import 'package:text_generation_video/app/view/text_to_video/text_to_video_page.dart';
import 'package:text_generation_video/app/view/web/web_page.dart';
import 'package:text_generation_video/config/global_config.dart';

import '../lifecycle/router_observer.dart';
import '../view/home/<USER>';
import '../view/login/login_page.dart';
import '../view/photo_restoration/photo_restoration_page.dart';
import '../view/record/record_page.dart';
// import '../view/splash/splash_page.dart';

/// 创建一个全局的 navigatorKey
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

/// 自定义路由监听组件
final CsRouteObserver csRouteObserver = CsRouteObserver();

/// home
const String home = "home";

/// login
const String login = "login";

/// creation
const String creation = "creation";

/// creation state
const String creationState = "creationState";

/// preview
const String preview = "preview";

/// setting
const String settingPage = "settingPage";
const String aboutUsPage = "aboutUsPage";
const String privacyCenterPage = "privacyCenterPage";
const String collectionMessagePage = "collectionMessagePage";
const String privacyPolicyPage = "privacyPolicyPage";
const String userAgreementPage = "userAgreementPage";
const String contactUsPage = "contactUsPage";

/// web page
const String webPage = "webPage";

/// power
const String computePowerPage = "computePowerPage";
const String computePowerDetailPage = "computePowerDetailPage";

/// agent
const String agentChatPage = "agentChatPage";
const String recordPage = "recordPage";
const String recordShowerPage = "recordShowerPage";

/// digital
const String publicDigitalDetailPage = "publicDigitalDetailPage";
const String customDigitalPage = "customDigitalPage";
const String makeVideoPage = "makeVideoPage";
const String makeVideoDigitalPage = "makeVideoDigitalPage";
const String makeVideoDigitalProcessPage = "makeVideoDigitalProcessPage";
const String digitalPreviewPage = "digitalPreviewPage";

/// same style
const String sameStyleDetailPage = "sameStyleDetailPage";

/// text to video
const String textToVideoPage = "textToVideoPage";

/// photo restoration
const String oldPhotoRestorationPage = "oldPhotoRestorationPage";
const String qualityRestorationPage = "qualityRestorationPage";
const String photoMattingPage = "photoMattingPage";
const String photoModificationPage = "photoModificationPage";
const String photoPortraitPage = "photoPortraitPage";
const String photoPortraitCategoryPage = "photoPortraitCategoryPage";

/// member
const String memberPage = "memberPage";

/// AI 对口型
const String petSingPage = "petSingPage";
const String syncSingPage = "syncSingPage";
const String cartoonDigimonPage = "cartoonDigimonPage";


/// The route configuration.
final GoRouter router = GoRouter(
  navigatorKey: navigatorKey,
  observers: [
    FlutterSmartDialog.observer,
    csRouteObserver,
  ],
  errorBuilder: (context, state) {
    return const NotFoundPage();
  },
  routes: <RouteBase>[
    GoRoute(
      path: '/',
      builder: (BuildContext context, GoRouterState state) {
        return const HomePage();
        // return GlobalConfig.agreePrivacy == true
        //     ? const HomePage()
        //     : const SplashPage();
      },
      routes: <RouteBase>[
        GoRoute(
          path: login,
          builder: (BuildContext context, GoRouterState state) {
            return const LoginPage();
          },
        ),
        GoRoute(
          path: creation,
          builder: (BuildContext context, GoRouterState state) {
            return const CreationPage();
          },
          // redirect: loginRedirect,
        ),
        GoRoute(
          path: creationState,
          builder: (BuildContext context, GoRouterState state) {
            return const CreationStatePage();
          },
          // redirect: loginRedirect,
        ),
        GoRoute(
          path: preview,
          builder: (BuildContext context, GoRouterState state) {
            CreationResult creationResult = state.extra as CreationResult;
            return PreviewPage(
              creationResult: creationResult,
            );
          },
        ),
        GoRoute(
          path: settingPage,
          builder: (BuildContext context, GoRouterState state) {
            return const SettingPage();
          },
          routes: [
            GoRoute(
              path: collectionMessagePage,
              builder: (BuildContext context, GoRouterState state) {
                return const CollectMessagePage();
              },
            ),
            GoRoute(
              path: privacyCenterPage,
              builder: (BuildContext context, GoRouterState state) {
                return const PrivacyCenterPage();
              },
            ),
          ],
        ),
        GoRoute(
          path: aboutUsPage,
          builder: (BuildContext context, GoRouterState state) {
            return const AboutUsPage();
          },
        ),
        GoRoute(
          path: contactUsPage,
          builder: (BuildContext context, GoRouterState state) {
            return const ContactUsPage();
          },
        ),
        GoRoute(
          path: privacyPolicyPage,
          builder: (BuildContext context, GoRouterState state) {
            return const PrivacyPolicyPage();
          },
        ),
        GoRoute(
          path: userAgreementPage,
          builder: (BuildContext context, GoRouterState state) {
            return const UserAgreementPage();
          },
        ),
        GoRoute(
          path: webPage,
          builder: (BuildContext context, GoRouterState state) {
            Map data = state.extra as Map;
            return WebPage(data: data);
          },
        ),
        GoRoute(
          path: computePowerPage,
          builder: (BuildContext context, GoRouterState state) {
            return const ComputePowerPage();
          },
        ),
        GoRoute(
          path: computePowerDetailPage,
          builder: (BuildContext context, GoRouterState state) {
            return const ComputePowerDetailPage();
          },
        ),
        GoRoute(
          path: agentChatPage,
          builder: (BuildContext context, GoRouterState state) {
            Map data = state.extra as Map;
            return AgentChatPage(data: data);
          },
        ),
        GoRoute(
          path: recordPage,
          builder: (BuildContext context, GoRouterState state) {
            Map data = state.extra as Map;
            return RecordPage(data: data);
          },
        ),
        GoRoute(
          path: recordShowerPage,
          pageBuilder: (BuildContext context, GoRouterState state) {
            Map data = state.extra as Map;
            return OpaqueNoTransitionPage(
              key: state.pageKey,
              name: state.name ?? state.path,
              arguments: <String, String>{
                ...state.pathParameters,
                ...state.uri.queryParameters
              },
              restorationId: state.pageKey.value,
              child: RecordShowerPage(mapData: data),
            );
          },
        ),
        GoRoute(
          path: publicDigitalDetailPage,
          builder: (BuildContext context, GoRouterState state) {
            PublicDigitalHuman data = state.extra as PublicDigitalHuman;
            return PublicDigitalDetailPage(publicDigitalHuman: data);
          },
        ),
        GoRoute(
          path: customDigitalPage,
          builder: (BuildContext context, GoRouterState state) {
            return const CustomDigitalPage();
          },
        ),
        GoRoute(
          path: makeVideoPage,
          builder: (BuildContext context, GoRouterState state) {
            return const MakeVideoPage();
          },
        ),
        GoRoute(
          path: makeVideoDigitalPage,
          builder: (BuildContext context, GoRouterState state) {
            var data = state.extra as PublicDigitalHuman?;
            return MakeVideoDigitalPage(publicDigitalHuman: data);
          },
        ),
        GoRoute(
          path: makeVideoDigitalProcessPage,
          builder: (BuildContext context, GoRouterState state) {
            return const MakeVideoDigitalProcessPage();
          },
        ),
        GoRoute(
          path: digitalPreviewPage,
          builder: (BuildContext context, GoRouterState state) {
            var videoUrl = state.extra as String?;
            return DigitalPreviewPage(videoUrl: videoUrl);
          },
        ),
        GoRoute(
          path: sameStyleDetailPage,
          pageBuilder: (BuildContext context, GoRouterState state) {
            Map data = state.extra as Map;
            return _createCustomTransitionPage(
              state,
              SameStyleDetailPage(data: data),
            );
          },
        ),
        GoRoute(
          path: textToVideoPage,
          builder: (BuildContext context, GoRouterState state) {
            return const TextToVideoPage();
          },
        ),
        GoRoute(
          path: oldPhotoRestorationPage,
          builder: (BuildContext context, GoRouterState state) {
            return const PhotoRestorationPage();
          },
        ),
        GoRoute(
          path: qualityRestorationPage,
          builder: (BuildContext context, GoRouterState state) {
            return const QualityRestorationPage();
          },
        ),
        GoRoute(
          path: photoMattingPage,
          builder: (BuildContext context, GoRouterState state) {
            return const PhotoMattingPage();
          },
        ),
        GoRoute(
          path: photoModificationPage,
          builder: (BuildContext context, GoRouterState state) {
            return const PhotoModificationPage();
          },
        ),
        GoRoute(
          path: photoPortraitPage,
          builder: (BuildContext context, GoRouterState state) {
            return const PhotoPortraitPage();
          },
        ),
        GoRoute(
          path: photoPortraitCategoryPage,
          builder: (BuildContext context, GoRouterState state) {
            return PhotoPortraitCategoryPage();
          },
        ),
        GoRoute(
          path: memberPage,
          builder: (BuildContext context, GoRouterState state) {
            return const MemberPage();
          },
        ),
        GoRoute(
          path: petSingPage,
          builder: (BuildContext context, GoRouterState state) {
            return const PetSingPage();
          },
        ),
        GoRoute(
          path: syncSingPage,
          builder: (BuildContext context, GoRouterState state) {
            return const SyncSingPage();
          },
        ),
        GoRoute(
          path: cartoonDigimonPage,
          builder: (BuildContext context, GoRouterState state) {
            return const CartoonDigimonPage();
          },
        ),
      ],
    ),
  ],
);

FutureOr<String?> loginRedirect(BuildContext context, GoRouterState state) {
  if (GlobalConfig.account != null) {
    return null;
  }
  return "/$login";
}

class OpaqueNoTransitionPage<T> extends CustomTransitionPage<T> {
  /// Constructor for a page with no transition functionality.
  const OpaqueNoTransitionPage({
    required super.child,
    super.name,
    super.arguments,
    super.restorationId,
    super.key,
  }) : super(
          transitionsBuilder: _transitionsBuilder,
          transitionDuration: Duration.zero,
          reverseTransitionDuration: Duration.zero,
          opaque: false,
        );

  static Widget _transitionsBuilder(
          BuildContext context,
          Animation<double> animation,
          Animation<double> secondaryAnimation,
          Widget child) =>
      child;
}

/// 可拖动Page
CustomTransitionPage _createCustomTransitionPage(
  GoRouterState state,
  Widget child,
) {
  return CustomTransitionPage(
    opaque: false,
    key: state.pageKey,
    name: state.name ?? state.path,
    arguments: <String, String>{
      ...state.pathParameters,
      ...state.uri.queryParameters
    },
    restorationId: state.pageKey.value,
    child: DragCardPage(child: child),
    transitionsBuilder: (context, a, b, c) {
      return c;
    },
  );
}
