import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/app/repository/modals/photo/photo_portrait.dart';
import 'package:text_generation_video/app/widgets/appbar/leading.dart';
import 'package:text_generation_video/config/icon_address.dart';

class PhotoPortraitPage extends ConsumerWidget {
  const PhotoPortraitPage({super.key});

  // 模拟数据 - 证件照Banner数据
  List<PhotoPortraitBanner> get mockBannerData => [
        PhotoPortraitBanner(
          caseImage: photoPortraitIdPhoto,
          jumpId: 1,
          sort: 1,
        ),
        PhotoPortraitBanner(
          caseImage: photoPortraitIdPhoto,
          jumpId: 2,
          sort: 2,
        ),
        PhotoPortraitBanner(
          caseImage: photoPortraitIdPhoto,
          jumpId: 3,
          sort: 3,
        ),
      ];

  // 模拟数据 - 写真分类数据
  List<PhotoPortraitCategory> get mockCategoryData => [
        PhotoPortraitCategory()
          ..caseName = "热门"
          ..id = 1
          ..sort = 1
          ..state = 1
          ..details = [
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/300?random=1"
              ..caseTitle = "热门写真1"
              ..casePrompt = "时尚热门写真风格"
              ..caseId = 101
              ..caseWidth = 200
              ..caseHeight = 300,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/350?random=2"
              ..caseTitle = "热门写真2"
              ..casePrompt = "流行写真风格"
              ..caseId = 102
              ..caseWidth = 200
              ..caseHeight = 350,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/280?random=3"
              ..caseTitle = "热门写真3"
              ..casePrompt = "经典写真风格"
              ..caseId = 103
              ..caseWidth = 200
              ..caseHeight = 280,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/320?random=4"
              ..caseTitle = "热门写真4"
              ..casePrompt = "现代写真风格"
              ..caseId = 104
              ..caseWidth = 200
              ..caseHeight = 320,
          ],
        PhotoPortraitCategory()
          ..caseName = "双重曝光"
          ..id = 2
          ..sort = 2
          ..state = 1
          ..details = [
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/290?random=5"
              ..caseTitle = "双重曝光1"
              ..casePrompt = "艺术双重曝光效果"
              ..caseId = 201
              ..caseWidth = 200
              ..caseHeight = 290,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/310?random=6"
              ..caseTitle = "双重曝光2"
              ..casePrompt = "创意双重曝光"
              ..caseId = 202
              ..caseWidth = 200
              ..caseHeight = 310,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/270?random=7"
              ..caseTitle = "双重曝光3"
              ..casePrompt = "梦幻双重曝光"
              ..caseId = 203
              ..caseWidth = 200
              ..caseHeight = 270,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/340?random=8"
              ..caseTitle = "双重曝光4"
              ..casePrompt = "专业双重曝光"
              ..caseId = 204
              ..caseWidth = 200
              ..caseHeight = 340,
          ],
        PhotoPortraitCategory()
          ..caseName = "网感大片"
          ..id = 3
          ..sort = 3
          ..state = 1
          ..details = [
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/300?random=9"
              ..caseTitle = "网感大片1"
              ..casePrompt = "时尚网感风格"
              ..caseId = 301
              ..caseWidth = 200
              ..caseHeight = 300,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/280?random=10"
              ..caseTitle = "网感大片2"
              ..casePrompt = "潮流网感风格"
              ..caseId = 302
              ..caseWidth = 200
              ..caseHeight = 280,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/320?random=11"
              ..caseTitle = "网感大片3"
              ..casePrompt = "个性网感风格"
              ..caseId = 303
              ..caseWidth = 200
              ..caseHeight = 320,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/290?random=12"
              ..caseTitle = "网感大片4"
              ..casePrompt = "酷炫网感风格"
              ..caseId = 304
              ..caseWidth = 200
              ..caseHeight = 290,
          ],
        PhotoPortraitCategory()
          ..caseName = "宠物写真"
          ..id = 4
          ..sort = 4
          ..state = 1
          ..details = [
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/310?random=13"
              ..caseTitle = "宠物写真1"
              ..casePrompt = "可爱宠物风格"
              ..caseId = 401
              ..caseWidth = 200
              ..caseHeight = 310,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/270?random=14"
              ..caseTitle = "宠物写真2"
              ..casePrompt = "萌宠写真风格"
              ..caseId = 402
              ..caseWidth = 200
              ..caseHeight = 270,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/330?random=15"
              ..caseTitle = "宠物写真3"
              ..casePrompt = "温馨宠物风格"
              ..caseId = 403
              ..caseWidth = 200
              ..caseHeight = 330,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/300?random=16"
              ..caseTitle = "宠物写真4"
              ..casePrompt = "活泼宠物风格"
              ..caseId = 404
              ..caseWidth = 200
              ..caseHeight = 300,
          ],
      ];

  Widget _buildHeader() {
    final bannerData = mockBannerData;
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: SizedBox(
        height: 80,
        child: ListView.separated(
          scrollDirection: Axis.horizontal,
          itemCount: bannerData.length,
          separatorBuilder: (context, index) => const SizedBox(width: 12),
          itemBuilder: (context, index) {
            final banner = bannerData[index];
            return _buildBannerItem(banner, index);
          },
        ),
      ),
    );
  }

  Widget _buildBannerItem(PhotoPortraitBanner banner, int index) {

    return GestureDetector(
      onTap: () => _handleBannerTap(banner),
      child: Container(
        // width: 140,
        child: Image.asset(banner.caseImage ?? ""),
      ),
    );
  }


  /// 处理Banner点击事件
  void _handleBannerTap(PhotoPortraitBanner banner) {
    // RouterUtil.checkLogin(
    //     // 这里需要context，但在ConsumerWidget中无法直接获取
    //     // 实际使用时需要传入context或使用其他方式处理
    //     // context,
    //     // call: () {
    //     //   // 根据jumpId跳转到对应的写真分类详情页
    //     //   // 这里可以根据实际需求实现跳转逻辑
    //     //   debugPrint("跳转到写真分类详情页，jumpId: ${banner.jumpId}");
    //     // },
    //     );
  }

  Widget _buildCategorySection(PhotoPortraitCategory category) {
    final details = category.details ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                category.caseName ?? "",
                style: const TextStyle(
                  fontSize: 18,
                  color: Colors.white,
                ),
              ),
              GestureDetector(
                onTap: () => _handleMoreTap(category),
                child: const Row(
                  children: [
                    Text(
                      "更多",
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(width: 4),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 8,
                      color: Colors.white,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 160,
          child: ListView.separated(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            scrollDirection: Axis.horizontal,
            itemCount: details.length,
            separatorBuilder: (context, index) => const SizedBox(width: 12),
            itemBuilder: (context, index) {
              final detail = details[index];
              return _buildCategoryItem(detail);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryItem(PhotoPortraitCategoryDetail detail) {
    return GestureDetector(
      onTap: () => _handleCategoryItemTap(detail),
      child: Container(
        width: 110,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Stack(
            fit: StackFit.expand,
            children: [
              CachedNetworkImage(
                imageUrl: detail.caseImage ?? "",
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: Colors.grey,
                  child: const Center(
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  color: Colors.grey,
                  child: const Center(
                    child: Icon(
                      Icons.image_not_supported,
                      color: Colors.white54,
                      size: 32,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 处理"更多"按钮点击事件
  void _handleMoreTap(PhotoPortraitCategory category) {
    // TODO: 跳转到分类详情页
    debugPrint("查看更多: ${category.caseName}");
  }

  /// 处理分类项目点击事件
  void _handleCategoryItemTap(PhotoPortraitCategoryDetail detail) {
    // TODO: 跳转到写真生成页面
    debugPrint("选择写真: ${detail.caseTitle}");
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        centerTitle: true,
        toolbarHeight: 44.h,
        title: const Text(
          "真人写真",
          style: TextStyle(
            fontSize: 16,
            color: Colors.white,
          ),
        ),
        leading: const Leading(
          color: Colors.white,
        ),
        actions: [
          InkWell(
            onTap: () {},
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
              decoration: BoxDecoration(
                color: const Color(0x30FFFFFF),
                borderRadius: BorderRadius.circular(13),
              ),
              child: const Text(
                "生成记录",
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 16),
            _buildHeader(),
            const SizedBox(height: 28),
            ...mockCategoryData.map((category) => Column(
                  children: [
                    _buildCategorySection(category),
                    const SizedBox(height: 32),
                  ],
                )),
            SizedBox(height: MediaQuery.of(context).padding.bottom + 20),
          ],
        ),
      ),
    );
  }
}
